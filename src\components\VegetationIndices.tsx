import React, { useState, useEffect } from 'react';
import { Leaf, BarChart3, TrendingUp, Calendar, Download, Eye, Layers, Activity } from 'lucide-react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface VegetationIndex {
  name: string;
  abbreviation: string;
  description: string;
  formula: string;
  range: { min: number; max: number };
  interpretation: {
    excellent: { min: number; max: number; color: string };
    good: { min: number; max: number; color: string };
    moderate: { min: number; max: number; color: string };
    poor: { min: number; max: number; color: string };
  };
}

interface IndexAnalysis {
  id: string;
  field_name: string;
  capture_date: string;
  indices: {
    ndvi: number;
    evi: number;
    savi: number;
    gndvi: number;
    ndre: number;
  };
  statistics: {
    mean: number;
    std: number;
    min: number;
    max: number;
    healthy_percentage: number;
    stressed_percentage: number;
  };
  plant_count?: number;
  plant_height_avg?: number;
  biomass_estimate?: number;
  growth_stage: string;
}

const VegetationIndices: React.FC = () => {
  const [selectedIndex, setSelectedIndex] = useState('ndvi');
  const [analyses, setAnalyses] = useState<IndexAnalysis[]>([]);
  const [selectedAnalysis, setSelectedAnalysis] = useState<IndexAnalysis | null>(null);

  const vegetationIndices: Record<string, VegetationIndex> = {
    ndvi: {
      name: 'Índice de Vegetação por Diferença Normalizada',
      abbreviation: 'NDVI',
      description: 'Indica a densidade e saúde da vegetação baseado na refletância NIR e vermelho',
      formula: '(NIR - Red) / (NIR + Red)',
      range: { min: -1, max: 1 },
      interpretation: {
        excellent: { min: 0.8, max: 1.0, color: '#22c55e' },
        good: { min: 0.6, max: 0.8, color: '#84cc16' },
        moderate: { min: 0.4, max: 0.6, color: '#eab308' },
        poor: { min: -1, max: 0.4, color: '#ef4444' }
      }
    },
    evi: {
      name: 'Índice de Vegetação Melhorado',
      abbreviation: 'EVI',
      description: 'Versão melhorada do NDVI, menos sensível à saturação e influência atmosférica',
      formula: '2.5 * (NIR - Red) / (NIR + 6*Red - 7.5*Blue + 1)',
      range: { min: -1, max: 1 },
      interpretation: {
        excellent: { min: 0.6, max: 1.0, color: '#22c55e' },
        good: { min: 0.4, max: 0.6, color: '#84cc16' },
        moderate: { min: 0.2, max: 0.4, color: '#eab308' },
        poor: { min: -1, max: 0.2, color: '#ef4444' }
      }
    },
    savi: {
      name: 'Índice de Vegetação Ajustado ao Solo',
      abbreviation: 'SAVI',
      description: 'Minimiza a influência do solo em áreas com baixa cobertura vegetal',
      formula: '(NIR - Red) / (NIR + Red + L) * (1 + L)',
      range: { min: -1, max: 1 },
      interpretation: {
        excellent: { min: 0.7, max: 1.0, color: '#22c55e' },
        good: { min: 0.5, max: 0.7, color: '#84cc16' },
        moderate: { min: 0.3, max: 0.5, color: '#eab308' },
        poor: { min: -1, max: 0.3, color: '#ef4444' }
      }
    },
    gndvi: {
      name: 'Índice de Vegetação Verde Normalizado',
      abbreviation: 'GNDVI',
      description: 'Sensível ao conteúdo de clorofila, útil para detectar estresse nutricional',
      formula: '(NIR - Green) / (NIR + Green)',
      range: { min: -1, max: 1 },
      interpretation: {
        excellent: { min: 0.7, max: 1.0, color: '#22c55e' },
        good: { min: 0.5, max: 0.7, color: '#84cc16' },
        moderate: { min: 0.3, max: 0.5, color: '#eab308' },
        poor: { min: -1, max: 0.3, color: '#ef4444' }
      }
    },
    ndre: {
      name: 'Diferença Normalizada Red Edge',
      abbreviation: 'NDRE',
      description: 'Sensível ao conteúdo de nitrogênio nas plantas',
      formula: '(NIR - RedEdge) / (NIR + RedEdge)',
      range: { min: -1, max: 1 },
      interpretation: {
        excellent: { min: 0.4, max: 1.0, color: '#22c55e' },
        good: { min: 0.3, max: 0.4, color: '#84cc16' },
        moderate: { min: 0.2, max: 0.3, color: '#eab308' },
        poor: { min: -1, max: 0.2, color: '#ef4444' }
      }
    }
  };

  // Dados simulados de análises
  useEffect(() => {
    const mockAnalyses: IndexAnalysis[] = [
      {
        id: 'analysis_001',
        field_name: 'Talhão A1 - Soja',
        capture_date: '2024-06-20T10:30:00',
        indices: {
          ndvi: 0.75,
          evi: 0.52,
          savi: 0.68,
          gndvi: 0.71,
          ndre: 0.35
        },
        statistics: {
          mean: 0.75,
          std: 0.12,
          min: 0.45,
          max: 0.89,
          healthy_percentage: 82.3,
          stressed_percentage: 17.7
        },
        plant_count: 12847,
        plant_height_avg: 45.2,
        biomass_estimate: 3.2,
        growth_stage: 'Floração (R1-R2)'
      },
      {
        id: 'analysis_002',
        field_name: 'Talhão B2 - Milho',
        capture_date: '2024-06-21T09:15:00',
        indices: {
          ndvi: 0.68,
          evi: 0.48,
          savi: 0.61,
          gndvi: 0.65,
          ndre: 0.31
        },
        statistics: {
          mean: 0.68,
          std: 0.15,
          min: 0.38,
          max: 0.85,
          healthy_percentage: 76.8,
          stressed_percentage: 23.2
        },
        plant_count: 8934,
        plant_height_avg: 78.5,
        biomass_estimate: 4.1,
        growth_stage: 'Pendoamento (VT)'
      },
      {
        id: 'analysis_003',
        field_name: 'Talhão C3 - Algodão',
        capture_date: '2024-06-19T14:45:00',
        indices: {
          ndvi: 0.82,
          evi: 0.58,
          savi: 0.74,
          gndvi: 0.78,
          ndre: 0.42
        },
        statistics: {
          mean: 0.82,
          std: 0.08,
          min: 0.65,
          max: 0.92,
          healthy_percentage: 91.5,
          stressed_percentage: 8.5
        },
        plant_count: 15623,
        plant_height_avg: 32.8,
        biomass_estimate: 2.8,
        growth_stage: 'Botão Floral'
      }
    ];
    setAnalyses(mockAnalyses);
    setSelectedAnalysis(mockAnalyses[0]);
  }, []);

  // Dados para gráfico temporal
  const temporalData = [
    { date: '2024-05-15', ndvi: 0.45, evi: 0.32, savi: 0.41, gndvi: 0.43, ndre: 0.22 },
    { date: '2024-05-30', ndvi: 0.58, evi: 0.41, savi: 0.52, gndvi: 0.55, ndre: 0.28 },
    { date: '2024-06-05', ndvi: 0.67, evi: 0.47, savi: 0.61, gndvi: 0.64, ndre: 0.32 },
    { date: '2024-06-15', ndvi: 0.73, evi: 0.51, savi: 0.66, gndvi: 0.69, ndre: 0.34 },
    { date: '2024-06-20', ndvi: 0.75, evi: 0.52, savi: 0.68, gndvi: 0.71, ndre: 0.35 }
  ];

  const getInterpretationLevel = (value: number, index: string) => {
    const indexData = vegetationIndices[index];
    if (value >= indexData.interpretation.excellent.min) return 'excellent';
    if (value >= indexData.interpretation.good.min) return 'good';
    if (value >= indexData.interpretation.moderate.min) return 'moderate';
    return 'poor';
  };

  const getInterpretationColor = (value: number, index: string) => {
    const level = getInterpretationLevel(value, index);
    return vegetationIndices[index].interpretation[level].color;
  };

  const getInterpretationLabel = (level: string) => {
    switch (level) {
      case 'excellent': return 'Excelente';
      case 'good': return 'Boa';
      case 'moderate': return 'Moderada';
      case 'poor': return 'Ruim';
      default: return 'N/A';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
              <Leaf className="w-6 h-6 text-green-600" />
              <span>Índices de Vegetação</span>
            </h2>
            <p className="text-gray-600 mt-1">
              Análise da saúde vegetal através de índices espectrais calculados a partir de imagens multiespectrais
            </p>
          </div>
        </div>

        {/* Seletor de Índices */}
        <div className="flex flex-wrap gap-2 mb-4">
          {Object.entries(vegetationIndices).map(([key, index]) => (
            <button
              key={key}
              onClick={() => setSelectedIndex(key)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedIndex === key
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {index.abbreviation}
            </button>
          ))}
        </div>

        {/* Informações do Índice Selecionado */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-900 mb-2">
            {vegetationIndices[selectedIndex].name} ({vegetationIndices[selectedIndex].abbreviation})
          </h3>
          <p className="text-green-800 text-sm mb-2">
            {vegetationIndices[selectedIndex].description}
          </p>
          <div className="text-xs text-green-700 font-mono bg-green-100 rounded px-2 py-1 inline-block">
            {vegetationIndices[selectedIndex].formula}
          </div>
        </div>
      </div>

      {/* Análises Recentes */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Análises Recentes</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {analyses.map((analysis) => (
            <div
              key={analysis.id}
              className={`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${
                selectedAnalysis?.id === analysis.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
              }`}
              onClick={() => setSelectedAnalysis(analysis)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium text-gray-900">{analysis.field_name}</h4>
                    <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">
                      {analysis.growth_stage}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 mb-3">
                    📅 {new Date(analysis.capture_date).toLocaleString('pt-BR')}
                  </div>
                  
                  {/* Índices */}
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                    {Object.entries(analysis.indices).map(([key, value]) => {
                      const level = getInterpretationLevel(value, key);
                      return (
                        <div key={key} className="bg-gray-50 rounded-lg p-2">
                          <div className="text-xs font-medium text-gray-700 uppercase">
                            {vegetationIndices[key]?.abbreviation || key}
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-bold text-gray-900">
                              {value.toFixed(3)}
                            </span>
                            <div
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: getInterpretationColor(value, key) }}
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                    <Eye className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Detalhes da Análise Selecionada */}
      {selectedAnalysis && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Estatísticas Detalhadas */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Estatísticas Detalhadas</h3>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="text-sm font-medium text-green-800">Saúde Vegetal</div>
                  <div className="text-lg font-bold text-green-700">
                    {selectedAnalysis.statistics.healthy_percentage.toFixed(1)}%
                  </div>
                </div>
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="text-sm font-medium text-red-800">Área com Estresse</div>
                  <div className="text-lg font-bold text-red-700">
                    {selectedAnalysis.statistics.stressed_percentage.toFixed(1)}%
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">NDVI Médio</label>
                  <div className="text-lg font-bold text-gray-900">
                    {selectedAnalysis.statistics.mean.toFixed(3)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Desvio Padrão</label>
                  <div className="text-lg font-bold text-gray-900">
                    {selectedAnalysis.statistics.std.toFixed(3)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Valor Mínimo</label>
                  <div className="text-lg font-bold text-gray-900">
                    {selectedAnalysis.statistics.min.toFixed(3)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Valor Máximo</label>
                  <div className="text-lg font-bold text-gray-900">
                    {selectedAnalysis.statistics.max.toFixed(3)}
                  </div>
                </div>
              </div>

              {selectedAnalysis.plant_count && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Contagem de Plantas</label>
                    <div className="text-lg font-bold text-gray-900">
                      {selectedAnalysis.plant_count.toLocaleString('pt-BR')}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Altura Média (cm)</label>
                    <div className="text-lg font-bold text-gray-900">
                      {selectedAnalysis.plant_height_avg?.toFixed(1)}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Biomassa (t/ha)</label>
                    <div className="text-lg font-bold text-gray-900">
                      {selectedAnalysis.biomass_estimate?.toFixed(1)}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Evolução Temporal */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Evolução Temporal</h3>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={temporalData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={(value) => new Date(value).toLocaleDateString('pt-BR', { month: 'short', day: 'numeric' })}
                />
                <YAxis domain={[0, 1]} />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleDateString('pt-BR')}
                  formatter={(value: number, name: string) => [value.toFixed(3), name.toUpperCase()]}
                />
                <Area 
                  type="monotone" 
                  dataKey={selectedIndex} 
                  stroke={getInterpretationColor(selectedAnalysis.indices[selectedIndex as keyof typeof selectedAnalysis.indices], selectedIndex)}
                  fill={getInterpretationColor(selectedAnalysis.indices[selectedIndex as keyof typeof selectedAnalysis.indices], selectedIndex)}
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}
    </div>
  );
};

export default VegetationIndices;
