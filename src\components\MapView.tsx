import React, { useState } from 'react';
import { MapPin, Layers, Satellite, Navigation, ZoomIn, ZoomOut, RotateCcw, Filter } from 'lucide-react';

const MapView: React.FC = () => {
  const [selectedLayer, setSelectedLayer] = useState('satellite');
  const [selectedFarm, setSelectedFarm] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  const farms = [
    {
      id: 1,
      name: 'Fazenda São João',
      coordinates: { lat: -12.5489, lng: -55.7183 },
      area: 2450,
      status: 'healthy',
      ndvi: 0.75,
      alerts: 1
    },
    {
      id: 2,
      name: 'Fazenda Esperança',
      coordinates: { lat: -15.5561, lng: -54.2961 },
      area: 1890,
      status: 'warning',
      ndvi: 0.62,
      alerts: 3
    }
  ];

  const layers = [
    { id: 'satellite', name: 'Satélite', icon: Satellite },
    { id: 'ndvi', name: 'ND<PERSON>', icon: Layers },
    { id: 'terrain', name: '<PERSON><PERSON><PERSON>', icon: MapPin },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'healthy': return 'Saudável';
      case 'warning': return 'Atenção';
      case 'critical': return 'Crítico';
      default: return 'Desconhecido';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mapa Interativo</h1>
          <p className="text-gray-600">Visualização geoespacial das propriedades e análises espectrais</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Map Container */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Map Controls */}
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-lg font-semibold text-gray-900">Visualização</h3>
                  <div className="flex items-center space-x-2">
                    {layers.map((layer) => {
                      const Icon = layer.icon;
                      return (
                        <button
                          key={layer.id}
                          onClick={() => setSelectedLayer(layer.id)}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            selectedLayer === layer.id
                              ? 'bg-emerald-100 text-emerald-700'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span>{layer.name}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <Filter className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <ZoomIn className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <ZoomOut className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <RotateCcw className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <Navigation className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Map Placeholder */}
              <div className="relative h-96 bg-gradient-to-br from-green-100 to-blue-100">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Mapa Interativo</h3>
                    <p className="text-gray-600 max-w-md">
                      Aqui seria exibido o mapa interativo com as propriedades rurais, 
                      análises NDVI e dados de sensoriamento remoto.
                    </p>
                  </div>
                </div>

                {/* Farm Markers Simulation */}
                {farms.map((farm, index) => (
                  <div
                    key={farm.id}
                    className={`absolute w-4 h-4 rounded-full cursor-pointer transform -translate-x-2 -translate-y-2 ${
                      getStatusColor(farm.status)
                    } ${selectedFarm === farm.id ? 'ring-4 ring-white ring-opacity-50' : ''}`}
                    style={{
                      left: `${30 + index * 40}%`,
                      top: `${40 + index * 20}%`
                    }}
                    onClick={() => setSelectedFarm(selectedFarm === farm.id ? null : farm.id)}
                  />
                ))}

                {/* Legend */}
                <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Legenda</h4>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-green-500" />
                      <span className="text-xs text-gray-600">Saudável</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-yellow-500" />
                      <span className="text-xs text-gray-600">Atenção</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500" />
                      <span className="text-xs text-gray-600">Crítico</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Filters Panel */}
              {showFilters && (
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Filtros</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Status da Vegetação</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="healthy">Saudável</option>
                        <option value="warning">Atenção</option>
                        <option value="critical">Crítico</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Faixa NDVI</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todas</option>
                        <option value="high">Alto (0.7-1.0)</option>
                        <option value="medium">Médio (0.4-0.7)</option>
                        <option value="low">Baixo (0.0-0.4)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Tipo de Cultivo</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="soja">Soja</option>
                        <option value="milho">Milho</option>
                        <option value="algodao">Algodão</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Farm List */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Propriedades</h3>
              <div className="space-y-3">
                {farms.map((farm) => (
                  <div
                    key={farm.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedFarm === farm.id
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedFarm(selectedFarm === farm.id ? null : farm.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{farm.name}</h4>
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(farm.status)}`} />
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Área: {farm.area} ha</p>
                      <p>NDVI: {farm.ndvi}</p>
                      <p>Status: {getStatusLabel(farm.status)}</p>
                      {farm.alerts > 0 && (
                        <p className="text-yellow-600">{farm.alerts} alerta(s)</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Farm Details */}
            {selectedFarm && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalhes</h3>
                {(() => {
                  const farm = farms.find(f => f.id === selectedFarm);
                  if (!farm) return null;
                  
                  return (
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Coordenadas</label>
                        <p className="text-sm text-gray-900">
                          {farm.coordinates.lat.toFixed(6)}, {farm.coordinates.lng.toFixed(6)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Área Total</label>
                        <p className="text-sm text-gray-900">{farm.area} hectares</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">NDVI Atual</label>
                        <p className="text-sm text-gray-900">{farm.ndvi}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status</label>
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(farm.status)}`} />
                          <span className="text-sm text-gray-900">{getStatusLabel(farm.status)}</span>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapView;
