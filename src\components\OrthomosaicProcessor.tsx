import React, { useState, useEffect } from 'react';
import { Map, Layers, Grid, Download, Eye, Settings, Zap, MapPin, Ruler, BarChart3 } from 'lucide-react';

interface ProcessingJob {
  id: string;
  name: string;
  field_name: string;
  images_count: number;
  area_hectares: number;
  resolution_cm: number;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  created_at: string;
  processing_time?: number;
  output_files?: {
    orthomosaic: string;
    dsm: string;
    dtm: string;
    point_cloud: string;
  };
}

interface ProcessingSettings {
  resolution: number;
  overlap: number;
  coordinate_system: string;
  output_format: string;
  generate_dsm: boolean;
  generate_dtm: boolean;
  generate_point_cloud: boolean;
}

const OrthomosaicProcessor: React.FC = () => {
  const [jobs, setJobs] = useState<ProcessingJob[]>([]);
  const [selectedJob, setSelectedJob] = useState<ProcessingJob | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState<ProcessingSettings>({
    resolution: 2.5,
    overlap: 80,
    coordinate_system: 'SIRGAS2000',
    output_format: 'GeoTIFF',
    generate_dsm: true,
    generate_dtm: true,
    generate_point_cloud: false
  });

  // Dados simulados de jobs de processamento
  useEffect(() => {
    const mockJobs: ProcessingJob[] = [
      {
        id: 'job_001',
        name: 'Ortomosaico Talhão A1 - Junho 2024',
        field_name: 'Talhão A1 - Soja',
        images_count: 247,
        area_hectares: 45.3,
        resolution_cm: 2.5,
        status: 'completed',
        progress: 100,
        created_at: '2024-06-20T10:30:00',
        processing_time: 1847,
        output_files: {
          orthomosaic: 'talhao_a1_orthomosaic.tif',
          dsm: 'talhao_a1_dsm.tif',
          dtm: 'talhao_a1_dtm.tif',
          point_cloud: 'talhao_a1_pointcloud.las'
        }
      },
      {
        id: 'job_002',
        name: 'Ortomosaico Talhão B2 - Junho 2024',
        field_name: 'Talhão B2 - Milho',
        images_count: 189,
        area_hectares: 32.1,
        resolution_cm: 3.0,
        status: 'processing',
        progress: 67,
        created_at: '2024-06-21T14:15:00'
      },
      {
        id: 'job_003',
        name: 'Ortomosaico Talhão C3 - Junho 2024',
        field_name: 'Talhão C3 - Algodão',
        images_count: 156,
        area_hectares: 28.7,
        resolution_cm: 2.0,
        status: 'queued',
        progress: 0,
        created_at: '2024-06-21T16:45:00'
      }
    ];
    setJobs(mockJobs);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-700 border-green-200';
      case 'processing': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'queued': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'failed': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed': return 'Concluído';
      case 'processing': return 'Processando';
      case 'queued': return 'Na fila';
      case 'failed': return 'Erro';
      default: return 'Desconhecido';
    }
  };

  const formatProcessingTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const startNewJob = () => {
    const newJob: ProcessingJob = {
      id: `job_${Date.now()}`,
      name: `Novo Ortomosaico - ${new Date().toLocaleDateString('pt-BR')}`,
      field_name: 'Talhão Selecionado',
      images_count: Math.floor(Math.random() * 200) + 100,
      area_hectares: Math.random() * 50 + 20,
      resolution_cm: settings.resolution,
      status: 'processing',
      progress: 0,
      created_at: new Date().toISOString()
    };

    setJobs(prev => [newJob, ...prev]);

    // Simular progresso
    const interval = setInterval(() => {
      setJobs(prev => prev.map(job => {
        if (job.id === newJob.id && job.status === 'processing') {
          const newProgress = Math.min(job.progress + Math.random() * 15, 100);
          if (newProgress >= 100) {
            clearInterval(interval);
            return {
              ...job,
              status: 'completed' as const,
              progress: 100,
              processing_time: Math.floor(Math.random() * 3600) + 600,
              output_files: {
                orthomosaic: `${job.field_name.toLowerCase().replace(/\s+/g, '_')}_orthomosaic.tif`,
                dsm: `${job.field_name.toLowerCase().replace(/\s+/g, '_')}_dsm.tif`,
                dtm: `${job.field_name.toLowerCase().replace(/\s+/g, '_')}_dtm.tif`,
                point_cloud: `${job.field_name.toLowerCase().replace(/\s+/g, '_')}_pointcloud.las`
              }
            };
          }
          return { ...job, progress: newProgress };
        }
        return job;
      }));
    }, 2000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
              <Grid className="w-6 h-6 text-blue-600" />
              <span>Processamento de Ortomosaicos</span>
            </h2>
            <p className="text-gray-600 mt-1">
              Geração de imagens georreferenciadas e ortogonalizadas a partir de múltiplas capturas
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              title="Configurações"
            >
              <Settings className="w-5 h-5" />
            </button>
            <button
              onClick={startNewJob}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Zap className="w-4 h-4" />
              <span>Novo Processamento</span>
            </button>
          </div>
        </div>

        {/* Configurações */}
        {showSettings && (
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <h3 className="font-medium text-gray-900 mb-3">Configurações de Processamento</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Resolução (cm/pixel)
                </label>
                <input
                  type="number"
                  value={settings.resolution}
                  onChange={(e) => setSettings({...settings, resolution: parseFloat(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  step="0.1"
                  min="0.5"
                  max="10"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sobreposição (%)
                </label>
                <input
                  type="number"
                  value={settings.overlap}
                  onChange={(e) => setSettings({...settings, overlap: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  min="60"
                  max="90"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sistema de Coordenadas
                </label>
                <select
                  value={settings.coordinate_system}
                  onChange={(e) => setSettings({...settings, coordinate_system: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                >
                  <option value="SIRGAS2000">SIRGAS 2000</option>
                  <option value="WGS84">WGS 84</option>
                  <option value="UTM">UTM</option>
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings.generate_dsm}
                  onChange={(e) => setSettings({...settings, generate_dsm: e.target.checked})}
                  className="rounded"
                />
                <label className="text-sm text-gray-700">Gerar DSM (Modelo Digital de Superfície)</label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings.generate_dtm}
                  onChange={(e) => setSettings({...settings, generate_dtm: e.target.checked})}
                  className="rounded"
                />
                <label className="text-sm text-gray-700">Gerar DTM (Modelo Digital do Terreno)</label>
              </div>
            </div>
          </div>
        )}

        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Total de Jobs</span>
            </div>
            <div className="text-lg font-bold text-blue-700">{jobs.length}</div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Eye className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Concluídos</span>
            </div>
            <div className="text-lg font-bold text-green-700">
              {jobs.filter(j => j.status === 'completed').length}
            </div>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">Processando</span>
            </div>
            <div className="text-lg font-bold text-yellow-700">
              {jobs.filter(j => j.status === 'processing').length}
            </div>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-800">Área Total</span>
            </div>
            <div className="text-lg font-bold text-purple-700">
              {jobs.reduce((acc, job) => acc + job.area_hectares, 0).toFixed(1)} ha
            </div>
          </div>
        </div>
      </div>

      {/* Lista de Jobs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Jobs de Processamento</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {jobs.map((job) => (
            <div
              key={job.id}
              className={`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${
                selectedJob?.id === job.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
              }`}
              onClick={() => setSelectedJob(selectedJob?.id === job.id ? null : job)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium text-gray-900">{job.name}</h4>
                    <span className={`text-xs px-2 py-1 rounded-full border ${getStatusColor(job.status)}`}>
                      {getStatusLabel(job.status)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>📍 {job.field_name} • {job.area_hectares.toFixed(1)} ha</div>
                    <div>📸 {job.images_count} imagens • Resolução: {job.resolution_cm} cm/pixel</div>
                    <div>🕒 Criado em {new Date(job.created_at).toLocaleString('pt-BR')}</div>
                    {job.processing_time && (
                      <div>⏱️ Tempo de processamento: {formatProcessingTime(job.processing_time)}</div>
                    )}
                  </div>
                  
                  {job.status === 'processing' && (
                    <div className="mt-3">
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span className="text-gray-600">Progresso</span>
                        <span className="font-medium">{job.progress.toFixed(0)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${job.progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
                
                {job.status === 'completed' && job.output_files && (
                  <div className="flex items-center space-x-2 ml-4">
                    <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                      <Download className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Detalhes do Job Selecionado */}
      {selectedJob && selectedJob.output_files && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Arquivos de Saída</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Map className="w-5 h-5 text-green-600" />
                <span className="font-medium text-gray-900">Ortomosaico</span>
              </div>
              <p className="text-sm text-gray-600 mb-2">Imagem georreferenciada e ortogonalizada</p>
              <p className="text-xs text-gray-500 mb-3">{selectedJob.output_files.orthomosaic}</p>
              <button className="w-full bg-green-600 text-white py-2 px-3 rounded-lg hover:bg-green-700 transition-colors text-sm">
                Visualizar/Download
              </button>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Layers className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-gray-900">DSM</span>
              </div>
              <p className="text-sm text-gray-600 mb-2">Modelo Digital de Superfície</p>
              <p className="text-xs text-gray-500 mb-3">{selectedJob.output_files.dsm}</p>
              <button className="w-full bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                Visualizar/Download
              </button>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Ruler className="w-5 h-5 text-purple-600" />
                <span className="font-medium text-gray-900">DTM</span>
              </div>
              <p className="text-sm text-gray-600 mb-2">Modelo Digital do Terreno</p>
              <p className="text-xs text-gray-500 mb-3">{selectedJob.output_files.dtm}</p>
              <button className="w-full bg-purple-600 text-white py-2 px-3 rounded-lg hover:bg-purple-700 transition-colors text-sm">
                Visualizar/Download
              </button>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Grid className="w-5 h-5 text-orange-600" />
                <span className="font-medium text-gray-900">Nuvem de Pontos</span>
              </div>
              <p className="text-sm text-gray-600 mb-2">Dados 3D de elevação</p>
              <p className="text-xs text-gray-500 mb-3">{selectedJob.output_files.point_cloud}</p>
              <button className="w-full bg-orange-600 text-white py-2 px-3 rounded-lg hover:bg-orange-700 transition-colors text-sm">
                Visualizar/Download
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrthomosaicProcessor;
