import React, { useState, useEffect } from 'react';
import { Map, Layers, Palette, Download, Eye, Settings, BarChart3, TrendingUp, Ruler, Target } from 'lucide-react';

interface ThematicMap {
  id: string;
  name: string;
  type: 'ndvi' | 'biomass' | 'height' | 'stress' | 'yield' | 'soil' | 'irrigation';
  field_name: string;
  capture_date: string;
  resolution_cm: number;
  area_hectares: number;
  color_scheme: string;
  min_value: number;
  max_value: number;
  mean_value: number;
  unit: string;
  description: string;
  thumbnail?: string;
  file_path: string;
}

interface ColorScheme {
  name: string;
  colors: string[];
  description: string;
}

const ThematicMaps: React.FC = () => {
  const [maps, setMaps] = useState<ThematicMap[]>([]);
  const [selectedMap, setSelectedMap] = useState<ThematicMap | null>(null);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [showSettings, setShowSettings] = useState(false);

  const mapTypes = {
    ndvi: { name: 'NDVI', icon: '🌱', color: 'green' },
    biomass: { name: 'Biomass<PERSON>', icon: '🌾', color: 'emerald' },
    height: { name: 'Altura das Plantas', icon: '📏', color: 'blue' },
    stress: { name: 'Estresse Hídrico', icon: '💧', color: 'cyan' },
    yield: { name: 'Produtividade', icon: '🚜', color: 'yellow' },
    soil: { name: 'Propriedades do Solo', icon: '🏔️', color: 'orange' },
    irrigation: { name: 'Necessidade de Irrigação', icon: '🚿', color: 'blue' }
  };

  const colorSchemes: Record<string, ColorScheme> = {
    viridis: {
      name: 'Viridis',
      colors: ['#440154', '#31688e', '#35b779', '#fde725'],
      description: 'Escala perceptualmente uniforme, ideal para dados científicos'
    },
    rdylgn: {
      name: 'Red-Yellow-Green',
      colors: ['#d73027', '#fee08b', '#d9ef8b', '#006837'],
      description: 'Vermelho (baixo) para verde (alto), intuitivo para vegetação'
    },
    spectral: {
      name: 'Spectral',
      colors: ['#9e0142', '#f46d43', '#fee08b', '#abdda4', '#5e4fa2'],
      description: 'Cores espectrais, boa para destacar variações'
    },
    terrain: {
      name: 'Terrain',
      colors: ['#00441b', '#40004b', '#762a83', '#9970ab', '#c2a5cf'],
      description: 'Cores naturais, ideal para mapas de elevação'
    }
  };

  // Dados simulados de mapas temáticos
  useEffect(() => {
    const mockMaps: ThematicMap[] = [
      {
        id: 'map_001',
        name: 'Mapa NDVI - Talhão A1',
        type: 'ndvi',
        field_name: 'Talhão A1 - Soja',
        capture_date: '2024-06-20T10:30:00',
        resolution_cm: 2.5,
        area_hectares: 45.3,
        color_scheme: 'rdylgn',
        min_value: 0.32,
        max_value: 0.89,
        mean_value: 0.75,
        unit: 'índice',
        description: 'Mapa de índice de vegetação mostrando a saúde e densidade da vegetação',
        file_path: 'maps/talhao_a1_ndvi_20240620.tif'
      },
      {
        id: 'map_002',
        name: 'Mapa de Biomassa - Talhão A1',
        type: 'biomass',
        field_name: 'Talhão A1 - Soja',
        capture_date: '2024-06-20T10:30:00',
        resolution_cm: 2.5,
        area_hectares: 45.3,
        color_scheme: 'viridis',
        min_value: 1.2,
        max_value: 4.8,
        mean_value: 3.2,
        unit: 't/ha',
        description: 'Estimativa de biomassa vegetal baseada em índices espectrais',
        file_path: 'maps/talhao_a1_biomass_20240620.tif'
      },
      {
        id: 'map_003',
        name: 'Mapa de Altura - Talhão B2',
        type: 'height',
        field_name: 'Talhão B2 - Milho',
        capture_date: '2024-06-21T09:15:00',
        resolution_cm: 3.0,
        area_hectares: 32.1,
        color_scheme: 'terrain',
        min_value: 35.2,
        max_value: 125.8,
        mean_value: 78.5,
        unit: 'cm',
        description: 'Altura das plantas calculada a partir do modelo digital de superfície',
        file_path: 'maps/talhao_b2_height_20240621.tif'
      },
      {
        id: 'map_004',
        name: 'Mapa de Estresse Hídrico - Talhão C3',
        type: 'stress',
        field_name: 'Talhão C3 - Algodão',
        capture_date: '2024-06-19T14:45:00',
        resolution_cm: 2.0,
        area_hectares: 28.7,
        color_scheme: 'spectral',
        min_value: 0.1,
        max_value: 0.8,
        mean_value: 0.3,
        unit: 'índice',
        description: 'Nível de estresse hídrico baseado em análise termal e espectral',
        file_path: 'maps/talhao_c3_stress_20240619.tif'
      },
      {
        id: 'map_005',
        name: 'Mapa de Produtividade Estimada - Talhão A1',
        type: 'yield',
        field_name: 'Talhão A1 - Soja',
        capture_date: '2024-06-20T10:30:00',
        resolution_cm: 2.5,
        area_hectares: 45.3,
        color_scheme: 'viridis',
        min_value: 2.1,
        max_value: 3.8,
        mean_value: 3.2,
        unit: 't/ha',
        description: 'Estimativa de produtividade baseada em modelos preditivos',
        file_path: 'maps/talhao_a1_yield_20240620.tif'
      }
    ];
    setMaps(mockMaps);
    setSelectedMap(mockMaps[0]);
  }, []);

  const filteredMaps = selectedType === 'all' 
    ? maps 
    : maps.filter(map => map.type === selectedType);

  const getTypeColor = (type: string) => {
    const typeInfo = mapTypes[type as keyof typeof mapTypes];
    return typeInfo ? typeInfo.color : 'gray';
  };

  const generateMap = (type: string) => {
    const newMap: ThematicMap = {
      id: `map_${Date.now()}`,
      name: `Novo Mapa ${mapTypes[type as keyof typeof mapTypes]?.name} - ${new Date().toLocaleDateString('pt-BR')}`,
      type: type as any,
      field_name: 'Talhão Selecionado',
      capture_date: new Date().toISOString(),
      resolution_cm: 2.5,
      area_hectares: Math.random() * 50 + 20,
      color_scheme: 'viridis',
      min_value: Math.random() * 0.5,
      max_value: Math.random() * 0.5 + 0.5,
      mean_value: Math.random() * 0.3 + 0.4,
      unit: type === 'height' ? 'cm' : type === 'biomass' || type === 'yield' ? 't/ha' : 'índice',
      description: `Mapa temático de ${mapTypes[type as keyof typeof mapTypes]?.name.toLowerCase()}`,
      file_path: `maps/new_${type}_${Date.now()}.tif`
    };

    setMaps(prev => [newMap, ...prev]);
    setSelectedMap(newMap);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
              <Map className="w-6 h-6 text-blue-600" />
              <span>Mapas Temáticos</span>
            </h2>
            <p className="text-gray-600 mt-1">
              Visualização espacial de dados agrícolas através de mapas coloridos e georreferenciados
            </p>
          </div>
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            title="Configurações"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>

        {/* Tipos de Mapa */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3 mb-4">
          <button
            onClick={() => setSelectedType('all')}
            className={`p-3 rounded-lg border text-center transition-colors ${
              selectedType === 'all'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="text-lg mb-1">🗺️</div>
            <div className="text-xs font-medium">Todos</div>
          </button>
          {Object.entries(mapTypes).map(([key, type]) => (
            <button
              key={key}
              onClick={() => setSelectedType(key)}
              className={`p-3 rounded-lg border text-center transition-colors ${
                selectedType === key
                  ? `border-${type.color}-500 bg-${type.color}-50 text-${type.color}-700`
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-lg mb-1">{type.icon}</div>
              <div className="text-xs font-medium">{type.name}</div>
            </button>
          ))}
        </div>

        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Map className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Total de Mapas</span>
            </div>
            <div className="text-lg font-bold text-blue-700">{maps.length}</div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Layers className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Área Mapeada</span>
            </div>
            <div className="text-lg font-bold text-green-700">
              {maps.reduce((acc, map) => acc + map.area_hectares, 0).toFixed(1)} ha
            </div>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Ruler className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-800">Resolução Média</span>
            </div>
            <div className="text-lg font-bold text-purple-700">
              {(maps.reduce((acc, map) => acc + map.resolution_cm, 0) / maps.length).toFixed(1)} cm
            </div>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">Tipos Únicos</span>
            </div>
            <div className="text-lg font-bold text-orange-700">
              {new Set(maps.map(m => m.type)).size}
            </div>
          </div>
        </div>
      </div>

      {/* Geração Rápida */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Geração Rápida de Mapas</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
          {Object.entries(mapTypes).map(([key, type]) => (
            <button
              key={key}
              onClick={() => generateMap(key)}
              className={`p-4 border border-gray-200 rounded-lg hover:border-${type.color}-300 hover:bg-${type.color}-50 transition-colors text-center`}
            >
              <div className="text-2xl mb-2">{type.icon}</div>
              <div className="text-sm font-medium text-gray-900">{type.name}</div>
              <div className="text-xs text-gray-600 mt-1">Gerar Mapa</div>
            </button>
          ))}
        </div>
      </div>

      {/* Lista de Mapas */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Mapas Disponíveis ({filteredMaps.length})
          </h3>
        </div>
        <div className="divide-y divide-gray-200">
          {filteredMaps.map((map) => (
            <div
              key={map.id}
              className={`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${
                selectedMap?.id === map.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
              }`}
              onClick={() => setSelectedMap(map)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className="text-xl">{mapTypes[map.type]?.icon}</span>
                    <h4 className="font-medium text-gray-900">{map.name}</h4>
                    <span className={`text-xs px-2 py-1 rounded-full bg-${getTypeColor(map.type)}-100 text-${getTypeColor(map.type)}-700`}>
                      {mapTypes[map.type]?.name}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>📍 {map.field_name} • {map.area_hectares.toFixed(1)} ha</div>
                    <div>📅 {new Date(map.capture_date).toLocaleString('pt-BR')}</div>
                    <div>🎯 Resolução: {map.resolution_cm} cm/pixel • Esquema: {colorSchemes[map.color_scheme]?.name}</div>
                    <div>📊 Valores: {map.min_value.toFixed(2)} - {map.max_value.toFixed(2)} {map.unit} (média: {map.mean_value.toFixed(2)})</div>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">{map.description}</p>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                    <Eye className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Detalhes do Mapa Selecionado */}
      {selectedMap && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalhes do Mapa</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Informações Técnicas */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Informações Técnicas</h4>
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Tipo de Mapa</label>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{mapTypes[selectedMap.type]?.icon}</span>
                      <span className="text-sm text-gray-900">{mapTypes[selectedMap.type]?.name}</span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Esquema de Cores</label>
                    <div className="text-sm text-gray-900">{colorSchemes[selectedMap.color_scheme]?.name}</div>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Valor Mínimo</label>
                    <div className="text-sm text-gray-900">{selectedMap.min_value.toFixed(3)} {selectedMap.unit}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Valor Médio</label>
                    <div className="text-sm text-gray-900">{selectedMap.mean_value.toFixed(3)} {selectedMap.unit}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Valor Máximo</label>
                    <div className="text-sm text-gray-900">{selectedMap.max_value.toFixed(3)} {selectedMap.unit}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Esquema de Cores */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Esquema de Cores</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Palette className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium">{colorSchemes[selectedMap.color_scheme]?.name}</span>
                </div>
                <div className="h-6 rounded-lg flex overflow-hidden">
                  {colorSchemes[selectedMap.color_scheme]?.colors.map((color, index) => (
                    <div
                      key={index}
                      className="flex-1"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
                <p className="text-xs text-gray-600">
                  {colorSchemes[selectedMap.color_scheme]?.description}
                </p>
                
                <div className="flex justify-between text-xs text-gray-500 mt-2">
                  <span>{selectedMap.min_value.toFixed(2)} {selectedMap.unit}</span>
                  <span>{selectedMap.max_value.toFixed(2)} {selectedMap.unit}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Ações */}
          <div className="mt-6 flex items-center space-x-3">
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
              <Eye className="w-4 h-4" />
              <span>Visualizar no Mapa</span>
            </button>
            <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Download GeoTIFF</span>
            </button>
            <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2">
              <Target className="w-4 h-4" />
              <span>Gerar Zonas de Manejo</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThematicMaps;
