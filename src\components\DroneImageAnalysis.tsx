import React, { useState, useEffect } from 'react';
import { Upload, Camera, Eye, BarChart3, Download, Al<PERSON><PERSON><PERSON>gle, <PERSON><PERSON><PERSON><PERSON>, Clock, Zap } from 'lucide-react';

interface Farm {
  id: number;
  name: string;
  total_area: number;
}

interface DroneImage {
  id: number;
  farm_id: number;
  filename: string;
  original_filename: string;
  image_type: 'rgb' | 'nir' | 'multispectral';
  latitude?: number;
  longitude?: number;
  capture_date?: string;
  analysis_results?: any;
  created_at: string;
}

interface AnalysisResult {
  ndvi?: {
    success: boolean;
    statistics: {
      mean_ndvi: number;
      vegetation_percentage: number;
      healthy_vegetation_percentage: number;
    };
    interpretation: {
      overall_health: string;
      recommendations: string[];
      alerts: string[];
    };
  };
  anomalies?: {
    success: boolean;
    total_anomalies: number;
    anomalies: any[];
  };
  report?: {
    summary: any;
    recommendations: string[];
  };
}

const DroneImageAnalysis: React.FC = () => {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarm, setSelectedFarm] = useState<Farm | null>(null);
  const [images, setImages] = useState<DroneImage[]>([]);
  const [selectedImages, setSelectedImages] = useState<number[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [analysisProgress, setAnalysisProgress] = useState<{ [key: number]: string }>({});
  const [dragActive, setDragActive] = useState(false);

  // Simular dados de fazendas
  useEffect(() => {
    const mockFarms: Farm[] = [
      { id: 1, name: 'Fazenda São João', total_area: 2450 },
      { id: 2, name: 'Fazenda Esperança', total_area: 1890 }
    ];
    setFarms(mockFarms);
  }, []);

  // Carregar imagens quando uma fazenda é selecionada
  useEffect(() => {
    if (selectedFarm) {
      const mockImages: DroneImage[] = [
        {
          id: 1,
          farm_id: selectedFarm.id,
          filename: 'drone_001.jpg',
          original_filename: 'IMG_001_RGB.jpg',
          image_type: 'rgb',
          latitude: -12.5489,
          longitude: -55.7183,
          capture_date: '2024-06-20T10:30:00',
          created_at: '2024-06-20T10:35:00',
          analysis_results: {
            ndvi: {
              success: true,
              statistics: {
                mean_ndvi: 0.65,
                vegetation_percentage: 78.5,
                healthy_vegetation_percentage: 65.2
              },
              interpretation: {
                overall_health: 'good',
                recommendations: ['Área com boa cobertura vegetal'],
                alerts: []
              }
            },
            anomalies: {
              success: true,
              total_anomalies: 2,
              anomalies: [
                { type: 'low_vegetation', percentage: 5.2 }
              ]
            }
          }
        },
        {
          id: 2,
          farm_id: selectedFarm.id,
          filename: 'drone_002.jpg',
          original_filename: 'IMG_002_NIR.jpg',
          image_type: 'nir',
          latitude: -12.5495,
          longitude: -55.7190,
          capture_date: '2024-06-20T10:32:00',
          created_at: '2024-06-20T10:37:00'
        }
      ];
      setImages(mockImages);
    }
  }, [selectedFarm]);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = (files: FileList) => {
    if (!selectedFarm) {
      alert('Selecione uma fazenda primeiro');
      return;
    }

    Array.from(files).forEach((file, index) => {
      // Simular upload
      const fileKey = `${file.name}_${Date.now()}_${index}`;
      setUploadProgress(prev => ({ ...prev, [fileKey]: 0 }));
      
      // Simular progresso de upload
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[fileKey] || 0;
          if (currentProgress >= 100) {
            clearInterval(interval);
            // Adicionar imagem à lista após upload
            const newImage: DroneImage = {
              id: Date.now() + index,
              farm_id: selectedFarm.id,
              filename: `uploaded_${Date.now()}_${index}.jpg`,
              original_filename: file.name,
              image_type: file.name.toLowerCase().includes('nir') ? 'nir' : 'rgb',
              capture_date: new Date().toISOString(),
              created_at: new Date().toISOString()
            };
            setImages(prev => [...prev, newImage]);
            
            // Remover progresso
            setTimeout(() => {
              setUploadProgress(prev => {
                const newProgress = { ...prev };
                delete newProgress[fileKey];
                return newProgress;
              });
            }, 1000);
            
            return prev;
          }
          return { ...prev, [fileKey]: currentProgress + 10 };
        });
      }, 200);
    });
  };

  const handleAnalyzeImage = (imageId: number, analysisType: 'ndvi' | 'anomalies' | 'complete') => {
    setAnalysisProgress(prev => ({ ...prev, [imageId]: 'processing' }));
    
    // Simular análise
    setTimeout(() => {
      const mockResults: AnalysisResult = {
        ndvi: {
          success: true,
          statistics: {
            mean_ndvi: Math.random() * 0.8 + 0.2,
            vegetation_percentage: Math.random() * 40 + 60,
            healthy_vegetation_percentage: Math.random() * 30 + 50
          },
          interpretation: {
            overall_health: ['excellent', 'good', 'moderate'][Math.floor(Math.random() * 3)],
            recommendations: ['Área com boa cobertura vegetal', 'Monitorar irrigação'],
            alerts: Math.random() > 0.7 ? ['Baixa atividade fotossintética detectada'] : []
          }
        },
        anomalies: {
          success: true,
          total_anomalies: Math.floor(Math.random() * 5),
          anomalies: []
        }
      };

      setImages(prev => prev.map(img => 
        img.id === imageId 
          ? { ...img, analysis_results: mockResults }
          : img
      ));
      
      setAnalysisProgress(prev => ({ ...prev, [imageId]: 'completed' }));
      
      setTimeout(() => {
        setAnalysisProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[imageId];
          return newProgress;
        });
      }, 2000);
    }, 3000);
  };

  const handleBatchAnalysis = () => {
    if (selectedImages.length === 0) {
      alert('Selecione pelo menos uma imagem');
      return;
    }
    
    selectedImages.forEach(imageId => {
      handleAnalyzeImage(imageId, 'complete');
    });
    
    setSelectedImages([]);
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'moderate': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getHealthLabel = (health: string) => {
    switch (health) {
      case 'excellent': return 'Excelente';
      case 'good': return 'Boa';
      case 'moderate': return 'Moderada';
      case 'poor': return 'Ruim';
      default: return 'Não analisada';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Análise de Imagens de Drone</h1>
          <p className="text-gray-600">Upload e análise de imagens espectrais (RGB e Infravermelho) com Inteligência Artificial</p>
        </div>

        {/* Seleção de Fazenda */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Selecionar Fazenda</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {farms.map((farm) => (
              <button
                key={farm.id}
                onClick={() => setSelectedFarm(farm)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  selectedFarm?.id === farm.id
                    ? 'border-emerald-500 bg-emerald-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <h3 className="font-medium text-gray-900">{farm.name}</h3>
                <p className="text-sm text-gray-600">{farm.total_area} hectares</p>
              </button>
            ))}
          </div>
        </div>

        {selectedFarm && (
          <>
            {/* Upload de Imagens */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Upload de Imagens</h2>
              
              <div
                className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
                  dragActive ? 'border-emerald-500 bg-emerald-50' : 'border-gray-300'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Arraste e solte suas imagens aqui
                </h3>
                <p className="text-gray-600 mb-4">
                  Suporte para imagens RGB, NIR (Infravermelho) e Multiespectrais
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Formatos aceitos: JPG, PNG, TIFF • Tamanho máximo: 50MB por arquivo
                </p>
                
                <label className="inline-flex items-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 cursor-pointer transition-colors">
                  <Camera className="w-4 h-4 mr-2" />
                  Selecionar Arquivos
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleFileInput}
                    className="hidden"
                  />
                </label>
              </div>

              {/* Progresso de Upload */}
              {Object.keys(uploadProgress).length > 0 && (
                <div className="mt-4 space-y-2">
                  {Object.entries(uploadProgress).map(([fileKey, progress]) => (
                    <div key={fileKey} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">
                          {fileKey.split('_')[0]}
                        </span>
                        <span className="text-sm text-gray-600">{progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-emerald-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Lista de Imagens */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Imagens Carregadas ({images.length})
                  </h2>
                  {selectedImages.length > 0 && (
                    <button
                      onClick={handleBatchAnalysis}
                      className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center space-x-2"
                    >
                      <Zap className="w-4 h-4" />
                      <span>Analisar Selecionadas ({selectedImages.length})</span>
                    </button>
                  )}
                </div>
              </div>

              <div className="p-6">
                {images.length > 0 ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {images.map((image) => {
                      const analysis = image.analysis_results;
                      const isProcessing = analysisProgress[image.id] === 'processing';
                      const isCompleted = analysisProgress[image.id] === 'completed';
                      const isSelected = selectedImages.includes(image.id);
                      
                      return (
                        <div
                          key={image.id}
                          className={`border rounded-xl p-4 transition-all ${
                            isSelected ? 'border-emerald-500 bg-emerald-50' : 'border-gray-200'
                          }`}
                        >
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center space-x-3">
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSelectedImages([...selectedImages, image.id]);
                                  } else {
                                    setSelectedImages(selectedImages.filter(id => id !== image.id));
                                  }
                                }}
                                className="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500"
                              />
                              <div>
                                <h3 className="font-medium text-gray-900">{image.original_filename}</h3>
                                <div className="flex items-center space-x-2 mt-1">
                                  <span className={`text-xs px-2 py-1 rounded-full ${
                                    image.image_type === 'rgb' ? 'bg-blue-100 text-blue-700' :
                                    image.image_type === 'nir' ? 'bg-red-100 text-red-700' :
                                    'bg-purple-100 text-purple-700'
                                  }`}>
                                    {image.image_type.toUpperCase()}
                                  </span>
                                  {image.capture_date && (
                                    <span className="text-xs text-gray-500">
                                      {new Date(image.capture_date).toLocaleDateString('pt-BR')}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            {isProcessing && (
                              <div className="flex items-center space-x-2 text-blue-600">
                                <Clock className="w-4 h-4 animate-spin" />
                                <span className="text-sm">Analisando...</span>
                              </div>
                            )}
                            
                            {isCompleted && (
                              <div className="flex items-center space-x-2 text-green-600">
                                <CheckCircle className="w-4 h-4" />
                                <span className="text-sm">Concluído</span>
                              </div>
                            )}
                          </div>

                          {/* Coordenadas */}
                          {image.latitude && image.longitude && (
                            <div className="text-sm text-gray-600 mb-3">
                              📍 {image.latitude.toFixed(6)}, {image.longitude.toFixed(6)}
                            </div>
                          )}

                          {/* Resultados da Análise */}
                          {analysis?.ndvi ? (
                            <div className="space-y-3">
                              <div className="bg-gray-50 rounded-lg p-3">
                                <h4 className="font-medium text-gray-900 mb-2">Análise NDVI</h4>
                                <div className="grid grid-cols-2 gap-3 text-sm">
                                  <div>
                                    <span className="text-gray-600">Saúde da Vegetação:</span>
                                    <div className={`inline-block ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                                      getHealthColor(analysis.ndvi.interpretation.overall_health)
                                    }`}>
                                      {getHealthLabel(analysis.ndvi.interpretation.overall_health)}
                                    </div>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">NDVI Médio:</span>
                                    <span className="ml-2 font-medium">
                                      {analysis.ndvi.statistics.mean_ndvi.toFixed(3)}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">Cobertura Vegetal:</span>
                                    <span className="ml-2 font-medium">
                                      {analysis.ndvi.statistics.vegetation_percentage.toFixed(1)}%
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">Vegetação Saudável:</span>
                                    <span className="ml-2 font-medium">
                                      {analysis.ndvi.statistics.healthy_vegetation_percentage.toFixed(1)}%
                                    </span>
                                  </div>
                                </div>
                                
                                {analysis.ndvi.interpretation.alerts.length > 0 && (
                                  <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                                    <div className="flex items-center space-x-2">
                                      <AlertTriangle className="w-4 h-4 text-yellow-600" />
                                      <span className="text-sm font-medium text-yellow-800">Alertas</span>
                                    </div>
                                    <ul className="mt-1 text-sm text-yellow-700">
                                      {analysis.ndvi.interpretation.alerts.map((alert, index) => (
                                        <li key={index}>• {alert}</li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>

                              {analysis.anomalies && (
                                <div className="bg-gray-50 rounded-lg p-3">
                                  <h4 className="font-medium text-gray-900 mb-2">Detecção de Anomalias</h4>
                                  <div className="text-sm">
                                    <span className="text-gray-600">Anomalias Detectadas:</span>
                                    <span className={`ml-2 font-medium ${
                                      analysis.anomalies.total_anomalies > 0 ? 'text-red-600' : 'text-green-600'
                                    }`}>
                                      {analysis.anomalies.total_anomalies}
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <button
                                onClick={() => handleAnalyzeImage(image.id, 'ndvi')}
                                disabled={isProcessing}
                                className="w-full bg-emerald-600 text-white py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
                              >
                                <BarChart3 className="w-4 h-4" />
                                <span>Analisar NDVI</span>
                              </button>
                              
                              <div className="grid grid-cols-2 gap-2">
                                <button
                                  onClick={() => handleAnalyzeImage(image.id, 'anomalies')}
                                  disabled={isProcessing}
                                  className="bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 text-sm flex items-center justify-center space-x-1"
                                >
                                  <Eye className="w-4 h-4" />
                                  <span>Anomalias</span>
                                </button>
                                
                                <button
                                  onClick={() => handleAnalyzeImage(image.id, 'complete')}
                                  disabled={isProcessing}
                                  className="bg-indigo-600 text-white py-2 px-3 rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 text-sm flex items-center justify-center space-x-1"
                                >
                                  <Zap className="w-4 h-4" />
                                  <span>Completa</span>
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Camera className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma imagem carregada</h3>
                    <p className="text-gray-600">Faça upload de imagens de drone para começar a análise</p>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DroneImageAnalysis;

