import React, { useState, useEffect } from 'react';
import { Upload, Camera, Eye, BarChart3, Download, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Clock, Zap, Bug, Droplets, Leaf, Target, MapPin, Layers, Activity, TrendingUp } from 'lucide-react';

interface Farm {
  id: number;
  name: string;
  total_area: number;
}

interface DroneImage {
  id: number;
  farm_id: number;
  field_name: string;
  filename: string;
  original_filename: string;
  image_type: 'rgb' | 'nir' | 'multispectral' | 'thermal';
  latitude?: number;
  longitude?: number;
  altitude?: number;
  capture_date?: string;
  weather_conditions?: string;
  analysis_results?: AnalysisResult;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
}

interface PestDetection {
  type: string;
  confidence: number;
  location: { x: number; y: number; width: number; height: number };
  severity: 'low' | 'medium' | 'high';
  recommended_action: string;
}

interface DiseaseDetection {
  disease_name: string;
  confidence: number;
  affected_area_percentage: number;
  severity: 'early' | 'moderate' | 'severe';
  location_zones: Array<{ x: number; y: number; radius: number }>;
  treatment_recommendation: string;
}

interface StressAnalysis {
  water_stress: {
    level: 'none' | 'mild' | 'moderate' | 'severe';
    affected_percentage: number;
    zones: Array<{ x: number; y: number; intensity: number }>;
  };
  nutrient_deficiency: {
    type: string[];
    severity: 'mild' | 'moderate' | 'severe';
    affected_areas: Array<{ x: number; y: number; deficiency_type: string }>;
  };
}

interface SprayRecommendation {
  zones: Array<{
    id: string;
    coordinates: Array<{ lat: number; lng: number }>;
    area_hectares: number;
    problem_type: 'pest' | 'disease' | 'nutrient' | 'weed';
    severity: 'low' | 'medium' | 'high';
    product_recommendation: string;
    application_rate: string;
    priority: number;
    estimated_cost: number;
  }>;
  total_area: number;
  estimated_savings: number;
  application_sequence: string[];
}

interface AnalysisResult {
  ndvi_analysis?: {
    success: boolean;
    mean_ndvi: number;
    vegetation_percentage: number;
    healthy_vegetation_percentage: number;
    stress_areas: Array<{ x: number; y: number; ndvi_value: number }>;
    growth_stage: string;
    biomass_estimate: number;
  };
  pest_detection?: {
    success: boolean;
    total_detections: number;
    pests_found: PestDetection[];
    risk_level: 'low' | 'medium' | 'high';
  };
  disease_analysis?: {
    success: boolean;
    diseases_detected: DiseaseDetection[];
    overall_health_score: number;
    infection_risk: 'low' | 'medium' | 'high';
  };
  stress_analysis?: StressAnalysis;
  weed_detection?: {
    weed_coverage_percentage: number;
    weed_types: string[];
    density_map: Array<{ x: number; y: number; density: number }>;
  };
  spray_recommendations?: SprayRecommendation;
  comparative_analysis?: {
    previous_flight_comparison: {
      ndvi_change: number;
      health_improvement: number;
      new_issues_detected: number;
    };
  };
}

const DroneImageAnalysis: React.FC = () => {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarm, setSelectedFarm] = useState<Farm | null>(null);
  const [images, setImages] = useState<DroneImage[]>([]);
  const [selectedImages, setSelectedImages] = useState<number[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [analysisProgress, setAnalysisProgress] = useState<{ [key: number]: string }>({});
  const [dragActive, setDragActive] = useState(false);

  // Dados das fazendas com talhões
  useEffect(() => {
    const mockFarms: Farm[] = [
      { id: 1, name: 'Fazenda São João', total_area: 2450 },
      { id: 2, name: 'Fazenda Esperança', total_area: 1890 },
      { id: 3, name: 'Fazenda Progresso', total_area: 3200 }
    ];
    setFarms(mockFarms);
  }, []);

  // Carregar imagens quando uma fazenda é selecionada
  useEffect(() => {
    if (selectedFarm) {
      const mockImages: DroneImage[] = [
        {
          id: 1,
          farm_id: selectedFarm.id,
          field_name: 'Talhão A1 - Soja',
          filename: 'drone_001_rgb.jpg',
          original_filename: 'TALHAO_A1_RGB_20240620.jpg',
          image_type: 'rgb',
          latitude: -12.5489,
          longitude: -55.7183,
          altitude: 120,
          capture_date: '2024-06-20T10:30:00',
          weather_conditions: 'Ensolarado, 28°C, vento 5km/h',
          processing_status: 'completed',
          created_at: '2024-06-20T10:35:00',
          analysis_results: {
            ndvi_analysis: {
              success: true,
              mean_ndvi: 0.72,
              vegetation_percentage: 85.3,
              healthy_vegetation_percentage: 78.1,
              stress_areas: [
                { x: 150, y: 200, ndvi_value: 0.45 },
                { x: 300, y: 150, ndvi_value: 0.38 }
              ],
              growth_stage: 'Floração (R1-R2)',
              biomass_estimate: 3.2
            },
            pest_detection: {
              success: true,
              total_detections: 3,
              pests_found: [
                {
                  type: 'Lagarta-da-soja',
                  confidence: 0.89,
                  location: { x: 180, y: 220, width: 50, height: 50 },
                  severity: 'medium',
                  recommended_action: 'Aplicação localizada de inseticida'
                }
              ],
              risk_level: 'medium'
            },
            disease_analysis: {
              success: true,
              diseases_detected: [
                {
                  disease_name: 'Ferrugem asiática',
                  confidence: 0.76,
                  affected_area_percentage: 12.5,
                  severity: 'early',
                  location_zones: [
                    { x: 200, y: 180, radius: 30 }
                  ],
                  treatment_recommendation: 'Fungicida preventivo'
                }
              ],
              overall_health_score: 82,
              infection_risk: 'medium'
            },
            stress_analysis: {
              water_stress: {
                level: 'mild',
                affected_percentage: 15.2,
                zones: [
                  { x: 250, y: 300, intensity: 0.6 }
                ]
              },
              nutrient_deficiency: {
                type: ['Potássio'],
                severity: 'mild',
                affected_areas: [
                  { x: 180, y: 250, deficiency_type: 'Potássio' }
                ]
              }
            },
            spray_recommendations: {
              zones: [
                {
                  id: 'zone_001',
                  coordinates: [
                    { lat: -12.5489, lng: -55.7183 },
                    { lat: -12.5492, lng: -55.7185 }
                  ],
                  area_hectares: 2.3,
                  problem_type: 'pest',
                  severity: 'medium',
                  product_recommendation: 'Inseticida Deltametrina 25g/L',
                  application_rate: '200ml/ha',
                  priority: 1,
                  estimated_cost: 145.50
                }
              ],
              total_area: 2.3,
              estimated_savings: 1250.00,
              application_sequence: ['Inseticida', 'Fungicida preventivo']
            }
          }
        },
        {
          id: 2,
          farm_id: selectedFarm.id,
          field_name: 'Talhão A1 - Soja',
          filename: 'drone_002_nir.jpg',
          original_filename: 'TALHAO_A1_NIR_20240620.jpg',
          image_type: 'nir',
          latitude: -12.5489,
          longitude: -55.7183,
          altitude: 120,
          capture_date: '2024-06-20T10:32:00',
          weather_conditions: 'Ensolarado, 28°C, vento 5km/h',
          processing_status: 'completed',
          created_at: '2024-06-20T10:37:00',
          analysis_results: {
            ndvi_analysis: {
              success: true,
              mean_ndvi: 0.74,
              vegetation_percentage: 87.1,
              healthy_vegetation_percentage: 81.3,
              stress_areas: [],
              growth_stage: 'Floração (R1-R2)',
              biomass_estimate: 3.4
            }
          }
        },
        {
          id: 3,
          farm_id: selectedFarm.id,
          field_name: 'Talhão B2 - Milho',
          filename: 'drone_003_multi.jpg',
          original_filename: 'TALHAO_B2_MULTI_20240621.jpg',
          image_type: 'multispectral',
          latitude: -12.5495,
          longitude: -55.7190,
          altitude: 100,
          capture_date: '2024-06-21T09:15:00',
          weather_conditions: 'Parcialmente nublado, 26°C',
          processing_status: 'processing',
          created_at: '2024-06-21T09:20:00'
        }
      ];
      setImages(mockImages);
    }
  }, [selectedFarm]);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = (files: FileList) => {
    if (!selectedFarm) {
      alert('Selecione uma fazenda primeiro');
      return;
    }

    Array.from(files).forEach((file, index) => {
      // Simular upload
      const fileKey = `${file.name}_${Date.now()}_${index}`;
      setUploadProgress(prev => ({ ...prev, [fileKey]: 0 }));
      
      // Simular progresso de upload
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[fileKey] || 0;
          if (currentProgress >= 100) {
            clearInterval(interval);
            // Adicionar imagem à lista após upload
            const newImage: DroneImage = {
              id: Date.now() + index,
              farm_id: selectedFarm.id,
              filename: `uploaded_${Date.now()}_${index}.jpg`,
              original_filename: file.name,
              image_type: file.name.toLowerCase().includes('nir') ? 'nir' : 'rgb',
              capture_date: new Date().toISOString(),
              created_at: new Date().toISOString()
            };
            setImages(prev => [...prev, newImage]);
            
            // Remover progresso
            setTimeout(() => {
              setUploadProgress(prev => {
                const newProgress = { ...prev };
                delete newProgress[fileKey];
                return newProgress;
              });
            }, 1000);
            
            return prev;
          }
          return { ...prev, [fileKey]: currentProgress + 10 };
        });
      }, 200);
    });
  };

  const handleAnalyzeImage = (imageId: number, analysisType: 'ndvi' | 'pest_disease' | 'stress' | 'complete') => {
    setAnalysisProgress(prev => ({ ...prev, [imageId]: 'processing' }));

    // Simular análise com IA
    setTimeout(() => {
      const pestTypes = ['Lagarta-da-soja', 'Percevejo', 'Mosca-branca', 'Ácaro-rajado'];
      const diseaseTypes = ['Ferrugem asiática', 'Mancha-alvo', 'Oídio', 'Antracnose'];
      const deficiencyTypes = ['Nitrogênio', 'Fósforo', 'Potássio', 'Magnésio'];

      const mockResults: AnalysisResult = {
        ndvi_analysis: {
          success: true,
          mean_ndvi: Math.random() * 0.6 + 0.4,
          vegetation_percentage: Math.random() * 30 + 70,
          healthy_vegetation_percentage: Math.random() * 25 + 65,
          stress_areas: Array.from({ length: Math.floor(Math.random() * 3) }, () => ({
            x: Math.floor(Math.random() * 400),
            y: Math.floor(Math.random() * 400),
            ndvi_value: Math.random() * 0.3 + 0.2
          })),
          growth_stage: ['Vegetativo (V4-V6)', 'Floração (R1-R2)', 'Enchimento de grãos (R3-R5)'][Math.floor(Math.random() * 3)],
          biomass_estimate: Math.random() * 2 + 2.5
        },
        pest_detection: {
          success: true,
          total_detections: Math.floor(Math.random() * 5),
          pests_found: Array.from({ length: Math.floor(Math.random() * 3) }, () => ({
            type: pestTypes[Math.floor(Math.random() * pestTypes.length)],
            confidence: Math.random() * 0.3 + 0.7,
            location: {
              x: Math.floor(Math.random() * 400),
              y: Math.floor(Math.random() * 400),
              width: Math.floor(Math.random() * 50) + 30,
              height: Math.floor(Math.random() * 50) + 30
            },
            severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high',
            recommended_action: 'Aplicação localizada de defensivo'
          })),
          risk_level: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high'
        },
        disease_analysis: {
          success: true,
          diseases_detected: Array.from({ length: Math.floor(Math.random() * 2) }, () => ({
            disease_name: diseaseTypes[Math.floor(Math.random() * diseaseTypes.length)],
            confidence: Math.random() * 0.3 + 0.6,
            affected_area_percentage: Math.random() * 20 + 5,
            severity: ['early', 'moderate', 'severe'][Math.floor(Math.random() * 3)] as 'early' | 'moderate' | 'severe',
            location_zones: [{
              x: Math.floor(Math.random() * 400),
              y: Math.floor(Math.random() * 400),
              radius: Math.floor(Math.random() * 50) + 20
            }],
            treatment_recommendation: 'Aplicação de fungicida específico'
          })),
          overall_health_score: Math.floor(Math.random() * 30) + 70,
          infection_risk: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high'
        },
        stress_analysis: {
          water_stress: {
            level: ['none', 'mild', 'moderate', 'severe'][Math.floor(Math.random() * 4)] as 'none' | 'mild' | 'moderate' | 'severe',
            affected_percentage: Math.random() * 25,
            zones: [{
              x: Math.floor(Math.random() * 400),
              y: Math.floor(Math.random() * 400),
              intensity: Math.random()
            }]
          },
          nutrient_deficiency: {
            type: [deficiencyTypes[Math.floor(Math.random() * deficiencyTypes.length)]],
            severity: ['mild', 'moderate', 'severe'][Math.floor(Math.random() * 3)] as 'mild' | 'moderate' | 'severe',
            affected_areas: [{
              x: Math.floor(Math.random() * 400),
              y: Math.floor(Math.random() * 400),
              deficiency_type: deficiencyTypes[Math.floor(Math.random() * deficiencyTypes.length)]
            }]
          }
        },
        spray_recommendations: {
          zones: [{
            id: `zone_${Date.now()}`,
            coordinates: [
              { lat: -12.5489 + (Math.random() - 0.5) * 0.01, lng: -55.7183 + (Math.random() - 0.5) * 0.01 }
            ],
            area_hectares: Math.random() * 5 + 1,
            problem_type: ['pest', 'disease', 'nutrient', 'weed'][Math.floor(Math.random() * 4)] as 'pest' | 'disease' | 'nutrient' | 'weed',
            severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high',
            product_recommendation: 'Produto específico recomendado',
            application_rate: `${Math.floor(Math.random() * 300) + 100}ml/ha`,
            priority: Math.floor(Math.random() * 3) + 1,
            estimated_cost: Math.random() * 500 + 100
          }],
          total_area: Math.random() * 10 + 2,
          estimated_savings: Math.random() * 2000 + 500,
          application_sequence: ['Inseticida', 'Fungicida', 'Fertilizante foliar']
        }
      };

      setImages(prev => prev.map(img =>
        img.id === imageId
          ? { ...img, analysis_results: mockResults, processing_status: 'completed' as const }
          : img
      ));

      setAnalysisProgress(prev => ({ ...prev, [imageId]: 'completed' }));

      setTimeout(() => {
        setAnalysisProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[imageId];
          return newProgress;
        });
      }, 2000);
    }, 4000);
  };

  const handleBatchAnalysis = () => {
    if (selectedImages.length === 0) {
      alert('Selecione pelo menos uma imagem para análise');
      return;
    }

    selectedImages.forEach((imageId, index) => {
      // Escalonar as análises para não sobrecarregar
      setTimeout(() => {
        handleAnalyzeImage(imageId, 'complete');
      }, index * 1000);
    });

    setSelectedImages([]);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      case 'severe': return 'text-red-700 bg-red-200';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityLabel = (severity: string) => {
    switch (severity) {
      case 'low': return 'Baixo';
      case 'medium': return 'Médio';
      case 'high': return 'Alto';
      case 'severe': return 'Severo';
      case 'early': return 'Inicial';
      case 'moderate': return 'Moderado';
      case 'mild': return 'Leve';
      case 'none': return 'Nenhum';
      default: return 'N/A';
    }
  };

  const getHealthScore = (score: number) => {
    if (score >= 90) return { label: 'Excelente', color: 'text-green-600' };
    if (score >= 80) return { label: 'Boa', color: 'text-blue-600' };
    if (score >= 70) return { label: 'Regular', color: 'text-yellow-600' };
    if (score >= 60) return { label: 'Ruim', color: 'text-orange-600' };
    return { label: 'Crítica', color: 'text-red-600' };
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Análise Inteligente de Imagens de Drone</h1>
          <p className="text-gray-600">
            Análise por IA de imagens espectrais para detecção precisa de pragas, doenças e estresse vegetal.
            Recomendações automáticas para pulverização localizada.
          </p>
          <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Bug className="w-5 h-5 text-emerald-600" />
                <span className="text-sm font-medium text-emerald-800">Detecção de Pragas</span>
              </div>
              <p className="text-xs text-emerald-600 mt-1">Identificação automática com 95% de precisão</p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Activity className="w-5 h-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Análise de Doenças</span>
              </div>
              <p className="text-xs text-blue-600 mt-1">Detecção precoce de patógenos</p>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Droplets className="w-5 h-5 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">Estresse Hídrico</span>
              </div>
              <p className="text-xs text-yellow-600 mt-1">Monitoramento de irrigação</p>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-purple-600" />
                <span className="text-sm font-medium text-purple-800">Aplicação Localizada</span>
              </div>
              <p className="text-xs text-purple-600 mt-1">Economia de até 70% em defensivos</p>
            </div>
          </div>
        </div>

        {/* Seleção de Fazenda */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Selecionar Fazenda</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {farms.map((farm) => (
              <button
                key={farm.id}
                onClick={() => setSelectedFarm(farm)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  selectedFarm?.id === farm.id
                    ? 'border-emerald-500 bg-emerald-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <h3 className="font-medium text-gray-900">{farm.name}</h3>
                <p className="text-sm text-gray-600">{farm.total_area} hectares</p>
              </button>
            ))}
          </div>
        </div>

        {selectedFarm && (
          <>
            {/* Upload de Imagens */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Upload de Imagens do Talhão</h2>
                <div className="text-sm text-gray-500">
                  Fazenda: <span className="font-medium text-gray-900">{selectedFarm?.name}</span>
                </div>
              </div>

              <div
                className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
                  dragActive ? 'border-emerald-500 bg-emerald-50' : 'border-gray-300'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Envie imagens do drone para análise por IA
                </h3>
                <p className="text-gray-600 mb-4">
                  Imagens RGB (visível), NIR (infravermelho), Multiespectral e Térmica
                </p>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4 text-xs">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                    <div className="font-medium text-green-800">RGB</div>
                    <div className="text-green-600">Detecção visual</div>
                  </div>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-2">
                    <div className="font-medium text-red-800">NIR</div>
                    <div className="text-red-600">Análise NDVI</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-2">
                    <div className="font-medium text-purple-800">Multiespectral</div>
                    <div className="text-purple-600">Estresse vegetal</div>
                  </div>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-2">
                    <div className="font-medium text-orange-800">Térmica</div>
                    <div className="text-orange-600">Estresse hídrico</div>
                  </div>
                </div>

                <p className="text-sm text-gray-500 mb-4">
                  Formatos: JPG, PNG, TIFF • Máximo: 50MB • Resolução mínima: 1920x1080
                </p>

                <label className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 cursor-pointer transition-colors">
                  <Camera className="w-5 h-5 mr-2" />
                  Selecionar Imagens do Drone
                  <input
                    type="file"
                    multiple
                    accept="image/*,.tiff,.tif"
                    onChange={handleFileInput}
                    className="hidden"
                  />
                </label>
              </div>

              {/* Progresso de Upload */}
              {Object.keys(uploadProgress).length > 0 && (
                <div className="mt-4 space-y-2">
                  {Object.entries(uploadProgress).map(([fileKey, progress]) => (
                    <div key={fileKey} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">
                          {fileKey.split('_')[0]}
                        </span>
                        <span className="text-sm text-gray-600">{progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-emerald-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Lista de Imagens */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">
                      Imagens para Análise por IA ({images.length})
                    </h2>
                    <p className="text-sm text-gray-600 mt-1">
                      Selecione imagens para análise automática de pragas, doenças e recomendações de pulverização
                    </p>
                  </div>
                  {selectedImages.length > 0 && (
                    <div className="flex items-center space-x-3">
                      <div className="text-sm text-gray-600">
                        {selectedImages.length} selecionada(s)
                      </div>
                      <button
                        onClick={handleBatchAnalysis}
                        className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center space-x-2"
                      >
                        <Zap className="w-4 h-4" />
                        <span>Analisar com IA</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div className="p-6">
                {images.length > 0 ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {images.map((image) => {
                      const analysis = image.analysis_results;
                      const isProcessing = analysisProgress[image.id] === 'processing';
                      const isCompleted = analysisProgress[image.id] === 'completed';
                      const isSelected = selectedImages.includes(image.id);
                      
                      return (
                        <div
                          key={image.id}
                          className={`border rounded-xl p-4 transition-all ${
                            isSelected ? 'border-emerald-500 bg-emerald-50' : 'border-gray-200'
                          }`}
                        >
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center space-x-3">
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSelectedImages([...selectedImages, image.id]);
                                  } else {
                                    setSelectedImages(selectedImages.filter(id => id !== image.id));
                                  }
                                }}
                                className="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500"
                              />
                              <div>
                                <h3 className="font-medium text-gray-900">{image.field_name}</h3>
                                <p className="text-sm text-gray-600">{image.original_filename}</p>
                                <div className="flex items-center space-x-2 mt-2">
                                  <span className={`text-xs px-2 py-1 rounded-full ${
                                    image.image_type === 'rgb' ? 'bg-green-100 text-green-700' :
                                    image.image_type === 'nir' ? 'bg-red-100 text-red-700' :
                                    image.image_type === 'multispectral' ? 'bg-purple-100 text-purple-700' :
                                    'bg-orange-100 text-orange-700'
                                  }`}>
                                    {image.image_type === 'rgb' ? 'RGB' :
                                     image.image_type === 'nir' ? 'NIR' :
                                     image.image_type === 'multispectral' ? 'MULTI' : 'TÉRMICA'}
                                  </span>
                                  {image.capture_date && (
                                    <span className="text-xs text-gray-500">
                                      {new Date(image.capture_date).toLocaleDateString('pt-BR')} às {new Date(image.capture_date).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
                                    </span>
                                  )}
                                  {image.altitude && (
                                    <span className="text-xs text-gray-500">
                                      {image.altitude}m
                                    </span>
                                  )}
                                </div>
                                {image.weather_conditions && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    🌤️ {image.weather_conditions}
                                  </p>
                                )}
                              </div>
                            </div>
                            
                            {isProcessing && (
                              <div className="flex items-center space-x-2 text-blue-600">
                                <Clock className="w-4 h-4 animate-spin" />
                                <span className="text-sm">Analisando...</span>
                              </div>
                            )}
                            
                            {isCompleted && (
                              <div className="flex items-center space-x-2 text-green-600">
                                <CheckCircle className="w-4 h-4" />
                                <span className="text-sm">Concluído</span>
                              </div>
                            )}
                          </div>

                          {/* Coordenadas */}
                          {image.latitude && image.longitude && (
                            <div className="text-sm text-gray-600 mb-3">
                              📍 {image.latitude.toFixed(6)}, {image.longitude.toFixed(6)}
                            </div>
                          )}

                          {/* Resultados da Análise por IA */}
                          {analysis?.ndvi_analysis ? (
                            <div className="space-y-4">
                              {/* Análise NDVI e Saúde Vegetal */}
                              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
                                <div className="flex items-center space-x-2 mb-3">
                                  <Leaf className="w-5 h-5 text-green-600" />
                                  <h4 className="font-semibold text-green-900">Análise NDVI e Biomassa</h4>
                                </div>
                                <div className="grid grid-cols-2 gap-3 text-sm">
                                  <div>
                                    <span className="text-gray-700">NDVI Médio:</span>
                                    <span className="ml-2 font-bold text-green-700">
                                      {analysis.ndvi_analysis.mean_ndvi.toFixed(3)}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-700">Cobertura Vegetal:</span>
                                    <span className="ml-2 font-bold text-green-700">
                                      {analysis.ndvi_analysis.vegetation_percentage.toFixed(1)}%
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-700">Estágio:</span>
                                    <span className="ml-2 font-medium text-green-800">
                                      {analysis.ndvi_analysis.growth_stage}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-700">Biomassa:</span>
                                    <span className="ml-2 font-bold text-green-700">
                                      {analysis.ndvi_analysis.biomass_estimate.toFixed(1)} t/ha
                                    </span>
                                  </div>
                                </div>
                                {analysis.ndvi_analysis.stress_areas.length > 0 && (
                                  <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                                    <div className="flex items-center space-x-2">
                                      <AlertTriangle className="w-4 h-4 text-yellow-600" />
                                      <span className="text-sm font-medium text-yellow-800">
                                        {analysis.ndvi_analysis.stress_areas.length} área(s) com estresse detectada(s)
                                      </span>
                                    </div>
                                  </div>
                                )}
                              </div>

                              {/* Detecção de Pragas */}
                              {analysis.pest_detection && (
                                <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-lg p-4 border border-red-200">
                                  <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center space-x-2">
                                      <Bug className="w-5 h-5 text-red-600" />
                                      <h4 className="font-semibold text-red-900">Detecção de Pragas</h4>
                                    </div>
                                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                                      getSeverityColor(analysis.pest_detection.risk_level)
                                    }`}>
                                      Risco {getSeverityLabel(analysis.pest_detection.risk_level)}
                                    </span>
                                  </div>
                                  <div className="text-sm">
                                    <div className="mb-2">
                                      <span className="text-gray-700">Pragas Detectadas:</span>
                                      <span className="ml-2 font-bold text-red-700">
                                        {analysis.pest_detection.total_detections}
                                      </span>
                                    </div>
                                    {analysis.pest_detection.pests_found.map((pest, index) => (
                                      <div key={index} className="bg-white rounded p-2 mb-2 border border-red-100">
                                        <div className="flex items-center justify-between">
                                          <span className="font-medium text-red-800">{pest.type}</span>
                                          <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(pest.severity)}`}>
                                            {getSeverityLabel(pest.severity)}
                                          </span>
                                        </div>
                                        <div className="text-xs text-gray-600 mt-1">
                                          Confiança: {(pest.confidence * 100).toFixed(1)}% • {pest.recommended_action}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Análise de Doenças */}
                              {analysis.disease_analysis && (
                                <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200">
                                  <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center space-x-2">
                                      <Activity className="w-5 h-5 text-purple-600" />
                                      <h4 className="font-semibold text-purple-900">Análise de Doenças</h4>
                                    </div>
                                    <div className="text-right">
                                      <div className={`text-sm font-bold ${getHealthScore(analysis.disease_analysis.overall_health_score).color}`}>
                                        Score: {analysis.disease_analysis.overall_health_score}
                                      </div>
                                      <div className="text-xs text-gray-600">
                                        {getHealthScore(analysis.disease_analysis.overall_health_score).label}
                                      </div>
                                    </div>
                                  </div>
                                  {analysis.disease_analysis.diseases_detected.map((disease, index) => (
                                    <div key={index} className="bg-white rounded p-2 mb-2 border border-purple-100">
                                      <div className="flex items-center justify-between">
                                        <span className="font-medium text-purple-800">{disease.disease_name}</span>
                                        <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(disease.severity)}`}>
                                          {getSeverityLabel(disease.severity)}
                                        </span>
                                      </div>
                                      <div className="text-xs text-gray-600 mt-1">
                                        Área afetada: {disease.affected_area_percentage.toFixed(1)}% •
                                        Confiança: {(disease.confidence * 100).toFixed(1)}%
                                      </div>
                                      <div className="text-xs text-purple-700 mt-1">
                                        💊 {disease.treatment_recommendation}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}

                              {/* Análise de Estresse */}
                              {analysis.stress_analysis && (
                                <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-200">
                                  <div className="flex items-center space-x-2 mb-3">
                                    <Droplets className="w-5 h-5 text-blue-600" />
                                    <h4 className="font-semibold text-blue-900">Análise de Estresse</h4>
                                  </div>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div className="bg-white rounded p-3 border border-blue-100">
                                      <div className="text-sm font-medium text-blue-800 mb-1">Estresse Hídrico</div>
                                      <div className="flex items-center justify-between">
                                        <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(analysis.stress_analysis.water_stress.level)}`}>
                                          {getSeverityLabel(analysis.stress_analysis.water_stress.level)}
                                        </span>
                                        <span className="text-sm font-medium">
                                          {analysis.stress_analysis.water_stress.affected_percentage.toFixed(1)}% da área
                                        </span>
                                      </div>
                                    </div>
                                    <div className="bg-white rounded p-3 border border-blue-100">
                                      <div className="text-sm font-medium text-blue-800 mb-1">Deficiência Nutricional</div>
                                      <div className="flex items-center justify-between">
                                        <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(analysis.stress_analysis.nutrient_deficiency.severity)}`}>
                                          {getSeverityLabel(analysis.stress_analysis.nutrient_deficiency.severity)}
                                        </span>
                                        <span className="text-sm">
                                          {analysis.stress_analysis.nutrient_deficiency.type.join(', ')}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}

                              {/* Recomendações de Pulverização */}
                              {analysis.spray_recommendations && (
                                <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg p-4 border border-emerald-200">
                                  <div className="flex items-center space-x-2 mb-3">
                                    <Target className="w-5 h-5 text-emerald-600" />
                                    <h4 className="font-semibold text-emerald-900">Recomendações de Pulverização Localizada</h4>
                                  </div>
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                                    <div className="bg-white rounded p-3 border border-emerald-100">
                                      <div className="text-sm font-medium text-emerald-800">Área Total</div>
                                      <div className="text-lg font-bold text-emerald-700">
                                        {analysis.spray_recommendations.total_area.toFixed(1)} ha
                                      </div>
                                    </div>
                                    <div className="bg-white rounded p-3 border border-emerald-100">
                                      <div className="text-sm font-medium text-emerald-800">Economia Estimada</div>
                                      <div className="text-lg font-bold text-emerald-700">
                                        R$ {analysis.spray_recommendations.estimated_savings.toFixed(0)}
                                      </div>
                                    </div>
                                    <div className="bg-white rounded p-3 border border-emerald-100">
                                      <div className="text-sm font-medium text-emerald-800">Zonas de Aplicação</div>
                                      <div className="text-lg font-bold text-emerald-700">
                                        {analysis.spray_recommendations.zones.length}
                                      </div>
                                    </div>
                                  </div>
                                  {analysis.spray_recommendations.zones.map((zone, index) => (
                                    <div key={index} className="bg-white rounded p-3 mb-2 border border-emerald-100">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="font-medium text-emerald-800">Zona {index + 1}</span>
                                        <div className="flex items-center space-x-2">
                                          <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(zone.severity)}`}>
                                            {getSeverityLabel(zone.severity)}
                                          </span>
                                          <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">
                                            Prioridade {zone.priority}
                                          </span>
                                        </div>
                                      </div>
                                      <div className="text-sm text-gray-700">
                                        <div>📍 Área: {zone.area_hectares.toFixed(1)} ha • Problema: {zone.problem_type}</div>
                                        <div>💊 {zone.product_recommendation} • {zone.application_rate}</div>
                                        <div>💰 Custo estimado: R$ {zone.estimated_cost.toFixed(2)}</div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="space-y-3">
                              <button
                                onClick={() => handleAnalyzeImage(image.id, 'complete')}
                                disabled={isProcessing}
                                className="w-full bg-gradient-to-r from-emerald-600 to-green-600 text-white py-3 px-4 rounded-lg hover:from-emerald-700 hover:to-green-700 transition-all disabled:opacity-50 flex items-center justify-center space-x-2 font-medium"
                              >
                                <Zap className="w-5 h-5" />
                                <span>Análise Completa por IA</span>
                              </button>

                              <div className="grid grid-cols-3 gap-2">
                                <button
                                  onClick={() => handleAnalyzeImage(image.id, 'ndvi')}
                                  disabled={isProcessing}
                                  className="bg-green-600 text-white py-2 px-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 text-xs flex items-center justify-center space-x-1"
                                >
                                  <Leaf className="w-3 h-3" />
                                  <span>NDVI</span>
                                </button>

                                <button
                                  onClick={() => handleAnalyzeImage(image.id, 'pest_disease')}
                                  disabled={isProcessing}
                                  className="bg-red-600 text-white py-2 px-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 text-xs flex items-center justify-center space-x-1"
                                >
                                  <Bug className="w-3 h-3" />
                                  <span>Pragas</span>
                                </button>

                                <button
                                  onClick={() => handleAnalyzeImage(image.id, 'stress')}
                                  disabled={isProcessing}
                                  className="bg-blue-600 text-white py-2 px-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 text-xs flex items-center justify-center space-x-1"
                                >
                                  <Droplets className="w-3 h-3" />
                                  <span>Estresse</span>
                                </button>
                              </div>

                              <div className="bg-gray-50 rounded-lg p-3 text-center">
                                <div className="text-xs text-gray-600 mb-1">Análise por IA inclui:</div>
                                <div className="text-xs text-gray-500">
                                  🔍 Detecção de pragas e doenças<br/>
                                  📊 Análise NDVI e biomassa<br/>
                                  💧 Estresse hídrico e nutricional<br/>
                                  🎯 Recomendações de pulverização
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="bg-gradient-to-br from-emerald-100 to-blue-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                      <Camera className="w-12 h-12 text-emerald-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Pronto para Análise por IA</h3>
                    <p className="text-gray-600 mb-4 max-w-md mx-auto">
                      Faça upload de imagens RGB, NIR ou Multiespectrais do seu talhão para análise automática de:
                    </p>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 max-w-lg mx-auto text-sm">
                      <div className="bg-red-50 border border-red-200 rounded-lg p-2">
                        <Bug className="w-4 h-4 text-red-600 mx-auto mb-1" />
                        <div className="text-red-800 font-medium">Pragas</div>
                      </div>
                      <div className="bg-purple-50 border border-purple-200 rounded-lg p-2">
                        <Activity className="w-4 h-4 text-purple-600 mx-auto mb-1" />
                        <div className="text-purple-800 font-medium">Doenças</div>
                      </div>
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-2">
                        <Droplets className="w-4 h-4 text-blue-600 mx-auto mb-1" />
                        <div className="text-blue-800 font-medium">Estresse</div>
                      </div>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                        <Target className="w-4 h-4 text-green-600 mx-auto mb-1" />
                        <div className="text-green-800 font-medium">Pulverização</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DroneImageAnalysis;

