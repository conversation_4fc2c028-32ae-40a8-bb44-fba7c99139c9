import React, { useState } from 'react';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import MapView from './components/MapView';
import Analytics from './components/Analytics';
import Reports from './components/Reports';
import FarmManagement from './components/FarmManagement';
import DroneImageAnalysis from './components/DroneImageAnalysis';

function App() {
  const [currentView, setCurrentView] = useState('dashboard');

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard />;
      case 'mapa':
        return <MapView />;
      case 'fazendas':
        return <FarmManagement />;
      case 'analise-imagens':
        return <DroneImageAnalysis />;
      case 'analytics':
        return <Analytics />;
      case 'relatorios':
        return <Reports />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar currentView={currentView} onViewChange={setCurrentView} />
      <main className="flex-1 overflow-auto">
        {renderCurrentView()}
      </main>
    </div>
  );
}

export default App;

